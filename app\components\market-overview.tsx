"use client";
import { useState, useEffect } from 'react';
import { motion } from "framer-motion"
import dynamic from "next/dynamic"
import { TrendingUp, TrendingDown, LineChart } from "lucide-react"

// Dynamically import TradingView widgets to avoid SSR issues
const TradingViewWidget = dynamic(
  () => import('react-tradingview-widget'),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-[300px] md:h-[400px] bg-gray-800/50 rounded-lg flex items-center justify-center">
        <div className="text-gray-400">Loading chart...</div>
      </div>
    )
  }
)

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

// Top-level server component
export function MarketOverview() {
  const isIPhone = useIsIPhone();
  return <MarketOverviewClient isIPhone={isIPhone} />;
}

function MarketOverviewClient({ isIPhone }) {
  const [isClient, setIsClient] = useState(false)
  const [chartError, setChartError] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Disable TradingView widget on iPhone to prevent performance issues and overheating
    if (isIPhone) {
      setChartError(true) // Force fallback display for iPhone
    }
  }, [isIPhone])

  const handleChartError = () => {
    setChartError(true)
  }

  return (
    <section className={`${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} relative section-tight bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36] overflow-hidden`}>
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5 md:opacity-10" />
        <div className="absolute top-0 right-0 w-[400px] md:w-[800px] h-[300px] md:h-[600px] bg-orange-500/10 rounded-full blur-[60px] md:blur-[120px]" />
        <div className="absolute bottom-0 left-0 w-[300px] md:w-[600px] h-[300px] md:h-[600px] bg-blue-500/10 rounded-full blur-[60px] md:blur-[120px]" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-4 md:mb-6"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent mb-4 md:mb-6">
            Live Market Overview
          </h2>
          <p className="text-base md:text-xl text-gray-300 max-w-3xl mx-auto">
            Stay updated with real-time market movements and trading opportunities
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
          {/* TradingView Chart */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="bg-white/5 backdrop-blur-xl rounded-xl md:rounded-2xl p-3 md:p-4 border border-white/10 h-[300px] md:h-[400px] hover:border-orange-500/30 transition-all duration-300"
          >
            {isClient && !chartError ? (
            <TradingViewWidget
              symbol="EURUSD"
              theme="dark"
              autosize
              style="1"
              locale="en"
            />
            ) : (
              <div className="w-full h-full bg-gray-800/50 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <LineChart className="w-8 md:w-12 h-8 md:h-12 text-gray-400 mx-auto mb-3 md:mb-4" />
                  <div className="text-gray-400 text-sm md:text-base">Market data temporarily unavailable</div>
                </div>
              </div>
            )}
          </motion.div>

          {/* Market Movers */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            className="space-y-3 md:space-y-4"
          >
            {[
              { pair: "EUR/USD", price: "1.0876", change: "+0.15%", trend: "up" },
              { pair: "GBP/USD", price: "1.2534", change: "-0.22%", trend: "down" },
              { pair: "USD/JPY", price: "148.45", change: "+0.33%", trend: "up" },
              { pair: "Gold", price: "2,023.50", change: "+0.45%", trend: "up" }
            ].map((market, index) => (
              <motion.div
                key={market.pair}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.05 }}
                className="bg-white/5 backdrop-blur-xl rounded-lg md:rounded-xl p-4 md:p-6 border border-white/10 hover:border-orange-500/30 transition-all duration-300 group"
              >
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-base md:text-xl font-bold text-white group-hover:text-orange-400 transition-colors">{market.pair}</h3>
                    <p className="text-sm md:text-xl font-bold text-blue-400">{market.price}</p>
                  </div>
                  <div
                    className={`flex items-center gap-2 ${
                    market.trend === "up" ? "text-green-400" : "text-red-400"
                    }`}
                  >
                    {market.trend === "up" ? (
                      <TrendingUp className="w-4 md:w-6 h-4 md:h-6" />
                    ) : (
                      <TrendingDown className="w-4 md:w-6 h-4 md:h-6" />
                    )}
                    <span className="text-sm md:text-lg font-bold">{market.change}</span>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
} 