import { But<PERSON> } from "@/components/ui/button"
import { Facebook, Instagram, Send, ExternalLink, Twitter, Users, Award } from 'lucide-react'
import { DiscordLogoIcon } from "@radix-ui/react-icons"
import Link from 'next/link'
import { motion } from "framer-motion"
import { Inter } from "next/font/google"

// Define Inter font with multiple weights
const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})

export const CommunityCallToAction = () => {
  return (
    <section className="relative section-tight bg-gradient-to-br from-[#0A0F1C] via-[#0F1A2E] to-[#121E36] overflow-hidden">
      {/* Professional Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5" />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-transparent to-orange-900/5" />
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent via-blue-500/5 to-transparent" />
      </div>

      {/* Floating geometric elements */}
      <div className="absolute top-20 left-10 w-32 h-32 border border-blue-500/20 rounded-full opacity-30" />
      <div className="absolute bottom-20 right-10 w-24 h-24 border border-orange-500/20 rounded-full opacity-30" />
      <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-green-500/20 rounded-full opacity-20" />

      <div className="relative container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-4 md:mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-500/10 to-blue-500/10 border border-orange-500/20 rounded-full px-6 py-2 mb-8"
            >
              <Users className="w-4 h-4 text-orange-400" />
              <span className="text-orange-400 text-sm font-medium">Join Our Community</span>
            </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 leading-tight"
          >
              <span className="text-white">Connect with </span>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600">
                Professional
            </span>
              <span className="text-white"> Traders</span>
          </motion.h2>

            <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed"
            >
              Join our exclusive community of successful traders. Access real-time market insights, 
              expert strategies, and connect with like-minded professionals to accelerate your trading success.
            </motion.p>
          </div>

          {/* Main CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="bg-gradient-to-r from-orange-500/10 via-blue-500/10 to-purple-500/10 border border-orange-500/20 rounded-3xl p-6 md:p-10 mb-4 md:mb-6"
          >
            <div className="text-center max-w-4xl mx-auto">
              <div className="flex items-center justify-center gap-3 mb-8">
                <Award className="w-10 h-10 text-orange-400" />
                <h3 className="text-3xl md:text-4xl font-bold text-white">Exclusive Discord Community</h3>
              </div>
              
              <p className="text-gray-300 text-xl mb-10 leading-relaxed">
                Get instant access to premium market analysis, advanced trading strategies, and exclusive promotions. 
                Connect with professional traders and take your trading to the next level.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link href="https://discord.gg/ZzG8demuuz" target="_blank" rel="noopener noreferrer">
                  <Button className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-10 py-5 rounded-xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-lg shadow-orange-500/25 hover:shadow-xl hover:shadow-orange-500/30 hover:scale-105 text-lg">
                    <DiscordLogoIcon className="w-6 h-6" />
                    Join Discord Community
                    <ExternalLink className="w-5 h-5" />
              </Button>
            </Link>
                
                <Button variant="outline" className="border-white/20 text-white hover:bg-white/10 px-10 py-5 rounded-xl font-semibold transition-all duration-300 text-lg">
                  Learn More
                </Button>
              </div>
            </div>
          </motion.div>

          {/* Social Media Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-center"
          >
            <h4 className="text-2xl font-semibold text-white mb-10">Follow Us on Social Media</h4>
            
            <div className="flex flex-wrap justify-center items-center gap-8">
            {[
              {
                Icon: Send,
                link: "https://t.me/fundedhorizon",
                label: "Telegram",
                  className: "hover:text-blue-400 hover:bg-blue-500/10",
                  bgColor: "bg-blue-500/20",
                  borderColor: "border-blue-500/30"
              },
              {
                Icon: Twitter,
                link: "https://twitter.com/fundedhorizon",
                label: "Twitter",
                  className: "hover:text-blue-400 hover:bg-blue-500/10",
                  bgColor: "bg-blue-500/20",
                  borderColor: "border-blue-500/30"
              },
              {
                Icon: Instagram,
                link: "https://www.instagram.com/fundedhorizon",
                label: "Instagram",
                  className: "hover:text-pink-400 hover:bg-pink-500/10",
                  bgColor: "bg-pink-500/20",
                  borderColor: "border-pink-500/30"
              },
              {
                Icon: Facebook,
                link: "https://www.facebook.com/fundedhorizon1",
                label: "Facebook",
                  className: "hover:text-blue-500 hover:bg-blue-500/10",
                  bgColor: "bg-blue-500/20",
                  borderColor: "border-blue-500/30"
              },
              {
                Icon: DiscordLogoIcon,
                link: "https://discord.gg/ZzG8demuuz",
                label: "Discord",
                  className: "hover:text-indigo-400 hover:bg-indigo-500/10",
                  bgColor: "bg-indigo-500/20",
                  borderColor: "border-indigo-500/30"
              }
              ].map(({ Icon, link, label, className, bgColor, borderColor }) => (
              <motion.a
                key={label}
                href={link}
                target="_blank"
                rel="noopener noreferrer"
                  className={`group p-5 rounded-2xl border transition-all duration-300 ${bgColor} ${borderColor} ${className}`}
                  whileHover={{ y: -5, scale: 1.05 }}
              >
                  <Icon className="w-10 h-10 text-white group-hover:scale-110 transition-transform duration-300" />
              </motion.a>
            ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
