"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { TrendingUp, ArrowR<PERSON>, Shield, Award } from 'lucide-react'
import { useState, useEffect } from 'react'

// Ultra-lightweight hero component for iPhone
export default function IPhoneOptimizedHero() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) {
    return (
      <div className="min-h-screen bg-[#0A0F1C] flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <section className="relative min-h-screen pt-16 overflow-hidden bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36]">
      {/* Simplified background - no complex animations */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 right-1/4 w-32 h-32 bg-orange-500/10 rounded-full blur-xl" />
        <div className="absolute bottom-1/4 left-1/4 w-32 h-32 bg-blue-500/10 rounded-full blur-xl" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center pt-20 pb-4">
          {/* Simple animated heading */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-4xl sm:text-5xl font-black leading-tight tracking-tight mb-6"
          >
            <span className="text-white block mb-2">
              TRADE
            </span>
            <span className="bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent block">
              LIKE A PRO
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-lg text-gray-300 max-w-2xl mx-auto mb-8 leading-relaxed"
          >
            Get funded up to <span className="text-orange-500 font-bold">$2.5M</span> with our professional trading platform.
          </motion.p>

          {/* Simple feature highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="grid grid-cols-2 gap-4 max-w-md mx-auto mb-8"
          >
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Shield className="w-4 h-4 text-green-400" />
              <span>Secure Trading</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <Award className="w-4 h-4 text-orange-400" />
              <span>Funded Accounts</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <TrendingUp className="w-4 h-4 text-blue-400" />
              <span>Live Markets</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-300">
              <ArrowRight className="w-4 h-4 text-purple-400" />
              <span>Fast Payouts</span>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 w-full sm:w-auto"
            >
              Start Trading Challenge
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-blue-500/50 text-blue-400 hover:bg-blue-500/10 font-semibold px-8 py-3 rounded-xl transition-all duration-300 w-full sm:w-auto"
            >
              Learn More
            </Button>
          </motion.div>

          {/* Simple stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="grid grid-cols-3 gap-4 max-w-lg mx-auto mt-12 pt-8 border-t border-white/10"
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">$2.5M</div>
              <div className="text-xs text-gray-400">Max Funding</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">24/7</div>
              <div className="text-xs text-gray-400">Support</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">10K+</div>
              <div className="text-xs text-gray-400">Traders</div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
