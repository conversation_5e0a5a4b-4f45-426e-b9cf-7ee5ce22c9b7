'use client'

import React, { useState, useEffect } from 'react'
import CountUp from 'react-countup'
import { motion } from 'framer-motion'
import { TrendingUp, Users, DollarSign, Award, HeadphonesIcon, UserCheck } from 'lucide-react'

function useIsIPhone() {
  const [isIPhone, setIsIPhone] = useState(false);
  useEffect(() => {
    setIsIPhone(/iPhone|iPad|iPod/.test(navigator.userAgent));
  }, []);
  return isIPhone;
}

const StatCard = ({ title, value, suffix = '', icon: Icon }) => (
  <motion.div
    initial={{ opacity: 0, y: 10 }}
    whileInView={{ opacity: 1, y: 0 }}
    viewport={{ once: true }}
    whileHover={{ scale: 1.03, y: -5 }}
    transition={{ duration: 0.4 }}
    className="relative group transform transition-all duration-300"
  >
    <div className="absolute -inset-0.5 bg-gradient-to-r from-orange-500 to-blue-600 rounded-2xl blur opacity-30 group-hover:opacity-70 transition-all duration-300"></div>
    <div className="relative bg-white/5 backdrop-blur-xl rounded-2xl p-8 border border-white/10 shadow-lg shadow-orange-500/20 group-hover:border-orange-500/30 transition-all duration-300 overflow-hidden">
      <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5 group-hover:opacity-10 transition-all duration-300"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-orange-500/10 rounded-full blur-xl group-hover:bg-blue-500/20 transition-all duration-300"></div>
      <div className="flex items-center justify-between mb-6 relative z-10">
        <h3 className="text-xl font-bold text-white">
          {title}
        </h3>
        <div className="p-3 rounded-xl bg-gradient-to-r from-orange-500 to-blue-600 group-hover:from-orange-400 group-hover:to-blue-500 transition-all duration-300 transform group-hover:rotate-3">
          <Icon className="w-6 h-6 text-white group-hover:scale-110 transition-all duration-300" />
        </div>
      </div>
      <div className="relative z-10">
        <CountUp
          end={value}
          duration={2}
          separator=","
          suffix={suffix}
          className="text-2xl sm:text-4xl font-bold bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent group-hover:from-orange-400 group-hover:to-blue-500 tracking-tight transition-all duration-300"
          enableScrollSpy
          scrollSpyOnce
        />
      </div>
    </div>
  </motion.div>
)

export default function StatisticsSection() {
  const isIPhone = useIsIPhone();
  return (
    <section className={`${isIPhone ? 'min-h-screen-ios' : 'min-h-screen'} relative min-h-[60vh] section-tight bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36] overflow-hidden`}>
      {/* Background effects */}
      <div className="absolute inset-0">
        <div className="absolute w-[800px] h-[800px] -left-1/4 -top-1/4 bg-orange-500/10 rounded-full blur-[80px]" />
        <div className="absolute w-[800px] h-[800px] -right-1/4 -bottom-1/4 bg-blue-500/10 rounded-full blur-[80px]" />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.3 }}
          className="text-center mb-4 md:mb-6"
        >
          <h2 className="text-5xl font-bold text-white mb-4">
            <span className="text-2xl sm:text-5xl">Our Performance <span className="bg-gradient-to-r from-orange-500 to-blue-600 bg-clip-text text-transparent">Impact</span></span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-orange-500 to-blue-600 mx-auto mb-6 rounded-full"></div>
          <p className="text-sm sm:text-base md:text-xl text-gray-300 max-w-3xl mx-auto">
            Join thousands of successful competitors who have transformed their performance career with our platform
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
          <StatCard title="Active Competitors" value={1983} icon={Users} />
          <StatCard title="Total Rewards" value={450789} suffix="$" icon={DollarSign} />
          <StatCard title="Success Rate" value={99} suffix="%" icon={TrendingUp} />
          <StatCard title="Customer Support" value={24} suffix="/7" icon={HeadphonesIcon} />
        </div>
      </div>
    </section>
  )
}
