"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"
import { TrendingUp, ArrowRight, Shield, Award, Globe, DollarSign, BarChart3, Target } from 'lucide-react'
import { useState, useEffect } from 'react'
import Image from 'next/image'

// Trading-related animated components
const TradingAnimation = () => {
  const [currentPrice, setCurrentPrice] = useState(1.2345)
  const [isUp, setIsUp] = useState(true)

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentPrice(prev => {
        const change = (Math.random() - 0.5) * 0.01
        const newPrice = prev + change
        setIsUp(change > 0)
        return parseFloat(newPrice.toFixed(4))
      })
    }, 2000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Animated candlesticks */}
      <div className="absolute top-20 left-10 opacity-20">
        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 3, repeat: Infinity }}
          className="flex items-end gap-1"
        >
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex flex-col items-center">
              <div className={`w-1 h-${Math.random() * 8 + 4} ${Math.random() > 0.5 ? 'bg-green-500' : 'bg-red-500'} rounded-sm`} />
              <div className={`w-2 h-${Math.random() * 4 + 2} ${Math.random() > 0.5 ? 'bg-green-500' : 'bg-red-500'} rounded-sm mt-1`} />
            </div>
          ))}
        </motion.div>
      </div>

      {/* Floating price indicators */}
      <motion.div
        animate={{ x: [0, 20, 0], y: [0, -10, 0] }}
        transition={{ duration: 4, repeat: Infinity }}
        className="absolute top-32 right-20 bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20"
      >
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isUp ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-white text-sm font-mono">EUR/USD</span>
          <span className={`text-sm font-bold ${isUp ? 'text-green-400' : 'text-red-400'}`}>
            {currentPrice}
          </span>
        </div>
      </motion.div>

      {/* Animated trading chart lines */}
      <svg className="absolute bottom-20 left-20 w-32 h-20 opacity-30" viewBox="0 0 128 80">
        <motion.path
          d="M0,40 Q32,20 64,40 T128,40"
          stroke="url(#gradient)"
          strokeWidth="2"
          fill="none"
          animate={{ d: ["M0,40 Q32,20 64,40 T128,40", "M0,40 Q32,60 64,40 T128,40", "M0,40 Q32,20 64,40 T128,40"] }}
          transition={{ duration: 6, repeat: Infinity }}
        />
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#f97316" />
            <stop offset="100%" stopColor="#3b82f6" />
          </linearGradient>
        </defs>
      </svg>

      {/* Floating statistics */}
      <motion.div
        animate={{ y: [0, -15, 0] }}
        transition={{ duration: 5, repeat: Infinity, delay: 1 }}
        className="absolute top-40 left-1/4 bg-gradient-to-r from-orange-500/20 to-blue-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20"
      >
        <div className="flex items-center gap-3">
          <DollarSign className="w-6 h-6 text-orange-400" />
          <div>
            <div className="text-white font-bold text-lg">$2.5M</div>
            <div className="text-gray-300 text-xs">Max Funding</div>
          </div>
        </div>
      </motion.div>

      {/* Animated success rate */}
      <motion.div
        animate={{ scale: [1, 1.05, 1] }}
        transition={{ duration: 3, repeat: Infinity, delay: 2 }}
        className="absolute bottom-32 right-1/3 bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20"
      >
        <div className="flex items-center gap-3">
          <Target className="w-6 h-6 text-green-400" />
          <div>
            <div className="text-white font-bold text-lg">94%</div>
            <div className="text-gray-300 text-xs">Success Rate</div>
          </div>
        </div>
      </motion.div>

      {/* Animated profit indicator */}
      <motion.div
        animate={{ rotate: [0, 5, -5, 0] }}
        transition={{ duration: 4, repeat: Infinity }}
        className="absolute top-1/2 right-10 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-lg p-4 border border-white/20"
      >
        <div className="flex items-center gap-3">
          <TrendingUp className="w-6 h-6 text-purple-400" />
          <div>
            <div className="text-white font-bold text-lg">90%</div>
            <div className="text-gray-300 text-xs">Profit Split</div>
          </div>
        </div>
      </motion.div>

      {/* Grid pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }} />
      </div>
    </div>
  )
}

export default function Hero() {
  return (
    <section className="relative min-h-screen pt-20 overflow-hidden bg-gradient-to-b from-[#0A0F1C] via-[#0F1A2E] to-[#121E36]">
      {/* Trading Animation Background */}
      <TradingAnimation />

      {/* Main Content */}
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center pt-20 pb-4">
            {/* Single XXL Bold Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-6xl md:text-7xl lg:text-8xl xl:text-9xl font-black leading-tight tracking-tight mb-8"
            >
              <span className="text-white block mb-4">
                TRADE
              </span>
              <span className="bg-gradient-to-r from-orange-500 via-orange-400 to-blue-600 bg-clip-text text-transparent block">
                LIKE A PRO
              </span>
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto mb-12 leading-relaxed"
            >
              Get funded up to <span className="text-orange-500 font-bold">$2.5M</span> with our professional trading platform. 
              Join thousands of successful traders worldwide.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-8 py-6 text-lg font-bold rounded-xl shadow-2xl shadow-orange-500/25 transition-all duration-300 hover:scale-105"
              >
                Start Trading Now
                <ArrowRight className="ml-2 h-6 w-6" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="bg-white/5 text-white border-white/20 hover:bg-white/10 px-8 py-6 text-lg font-bold rounded-xl transition-all duration-300 hover:scale-105"
              >
                Learn More
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Bottom wave effect */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-20">
          <path
            d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
            fill="url(#wave-gradient)"
          />
          <path
            d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
            fill="url(#wave-gradient)"
          />
          <path
            d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            fill="url(#wave-gradient)"
          />
          <defs>
            <linearGradient id="wave-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#f97316" />
              <stop offset="50%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#f97316" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </section>
  )
}
